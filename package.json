{"name": "whiteboard", "version": "1.1.0-beta.1", "license": "AGPL-3.0-or-later", "private": true, "type": "module", "main": "src/main.tsx", "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=8192' vite --mode production build", "dev": "vite --mode development build", "watch": "vite --mode development build --watch", "lint": "eslint --ext .js,.mjs,.ts,.tsx,.vue src websocket_server tests/integration ", "lint:fix": "eslint --ext .js,.mjs,.ts,.tsx,.vue src websocket_server tests/integration --fix", "stylelint": "stylelint 'src/**/*.{css,scss,sass}'", "stylelint:fix": "stylelint 'src/**/*.{css,scss,sass}' --fix", "server:start": "node websocket_server/main.js", "server:watch": "nodemon websocket_server/main.js", "test": "vitest run", "test:watch": "vitest", "start:nextcloud": "node playwright/start-nextcloud-server.mjs"}, "dependencies": {"@excalidraw/excalidraw": "^0.17.6", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@mdi/svg": "^7.4.47", "@nextcloud/auth": "^2.4.0", "@nextcloud/axios": "^2.5.1", "@nextcloud/dialogs": "^6.3.0", "@nextcloud/event-bus": "^3.3.2", "@nextcloud/files": "^3.10.2", "@nextcloud/initial-state": "^2.2.0", "@nextcloud/l10n": "^3.2.0", "@nextcloud/router": "^3.0.1", "@nextcloud/sharing": "^0.2.4", "@nextcloud/vue": "^8.23.1", "@socket.io/redis-streams-adapter": "^0.2.2", "axios": "^1.9.0", "dexie": "^4.0.11", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lru-cache": "^11.0.2", "node-fetch": "^3.3.2", "persist": "^0.2.7", "prom-client": "^15.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "redis": "^5.1.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "socket.io-prometheus": "^0.3.0", "vue": "^2.7.16", "zustand": "^5.0.5"}, "devDependencies": {"@nextcloud/browserslist-config": "^3.0.1", "@nextcloud/e2e-test-server": "^0.2.1", "@nextcloud/eslint-config": "^8.4.2", "@nextcloud/stylelint-config": "^3.0.1", "@nextcloud/vite-config": "^1.5.6", "@playwright/test": "^1.52.0", "@types/lodash": "^4.17.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.5.0", "@vue/tsconfig": "^0.5.1", "nodemon": "^3.1.10", "prettier": "^3.5.3", "stylelint-config-css-modules": "^4.4.0", "typescript": "^5.8.3", "typescript-plugin-css-modules": "^5.1.0", "vite": "^6.3.5", "vite-plugin-static-copy": "^2.3.0", "vitest": "^3.1.3"}, "prettier": {"useTabs": true, "semi": false, "singleQuote": true, "bracketSameLine": true, "tabWidth": 4}, "browserslist": ["extends @nextcloud/browserslist-config"], "eslintConfig": {"extends": ["@nextcloud/eslint-config/typescript"], "rules": {"jsdoc/require-jsdoc": "off"}}, "engines": {"node": "^23", "npm": "^10"}}
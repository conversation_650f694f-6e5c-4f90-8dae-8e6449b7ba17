/* eslint-disable no-console */
/* eslint-disable n/no-process-exit */

/**
 * SPDX-FileCopyrightText: 2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-License-Identifier: AGPL-3.0-or-later
 */
import ServerManager from './ServerManager.js'
import Config from './Config.js'

async function main() {
	try {
		const serverManager = new ServerManager()

		// Initialize all components including Redis connection
		await serverManager.initialize()

		// Start the server
		await serverManager.start()

		console.log(`Server started successfully on port ${Config.PORT}`)

		// Setup Redis adapter after server is running
		if (serverManager.socketManager && serverManager.socketManager.setupAdapterAfterStart) {
			console.log('Setting up Redis adapter after server start...')
			await serverManager.socketManager.setupAdapterAfterStart()
		}

		process.on('SIGTERM', () => serverManager.gracefulShutdown())
		process.on('SIGINT', () => serverManager.gracefulShutdown())
	} catch (error) {
		console.error('Failed to start server:', error)
		process.exit(1)
	}
}

main()
